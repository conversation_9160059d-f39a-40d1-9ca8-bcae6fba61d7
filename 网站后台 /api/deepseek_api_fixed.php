<?php
/**
 * DeepSeek AI客服API接口 - 修复版本
 * 修复了putenv函数被禁用的问题
 * 
 * @version 1.1.0
 * <AUTHOR> Assistant
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * 简化的配置类
 */
class SimpleConfig {
    private $dbConfig;
    
    public function __construct() {
        $this->dbConfig = [
            'host' => '127.0.0.1',
            'port' => '3306',
            'dbname' => 'xiaomeihuakefu_c',
            'user' => 'xiaomeihuakefu_c',
            'pass' => '7Da5F1Xx995cxYz8'
        ];
    }
    
    public function getDatabaseConfig() {
        return $this->dbConfig;
    }
    
    public function getApiSecretKey() {
        return 'xiaomeihua_api_secret_key_2025';
    }
}

/**
 * 简化的API基础类
 */
class SimpleApiBase {
    protected $config;
    protected $db;
    protected $request;
    
    public function __construct() {
        $this->config = new SimpleConfig();
        $this->connectDatabase();
        $this->request = $this->getRequestData();
    }
    
    protected function connectDatabase() {
        try {
            $dbConfig = $this->config->getDatabaseConfig();
            $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset=utf8mb4";
            $this->db = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            $this->respondError('数据库连接失败: ' . $e->getMessage(), 500);
        }
    }
    
    protected function getRequestData() {
        $data = [];
        
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            $data = $_GET;
        } else {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') !== false) {
                $input = file_get_contents('php://input');
                if (!empty($input)) {
                    $data = json_decode($input, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $this->respondError('无效的JSON数据', 400);
                    }
                }
            } else {
                $data = $_POST;
            }
        }
        
        return $data;
    }
    
    protected function respondSuccess($data = [], $message = 'Success', $code = 200) {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        http_response_code($code);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    protected function respondError($message = 'Error', $code = 400) {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        http_response_code($code);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * DeepSeek API类
 */
class DeepSeekAPIFixed extends SimpleApiBase {
    
    public function __construct() {
        parent::__construct();
        $this->handleRequest();
    }
    
    private function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? $this->request['action'] ?? '';
        
        // 验证license_key（简化版本，仅用于测试）
        $licenseKey = $this->request['license_key'] ?? $_GET['license_key'] ?? '';
        if (empty($licenseKey) && $action !== 'info') {
            $this->respondError('缺少license_key参数', 400);
        }
        
        switch ($action) {
            case 'info':
                $this->getApiInfo();
                break;
            case 'test':
                $this->testAPI();
                break;
            case 'chat':
                $this->handleChat();
                break;
            case 'validate_key':
                $this->validateApiKey();
                break;
            case 'config':
                $this->getConfig();
                break;
            case 'status':
                $this->getStatus();
                break;
            default:
                $this->respondError('未知的操作类型: ' . $action, 400);
        }
    }
    
    private function getApiInfo() {
        $info = [
            'api_name' => 'DeepSeek AI客服API - 修复版本',
            'version' => '1.1.0',
            'service_type' => 'deepseek',
            'status' => 'active',
            'endpoints' => [
                'GET /deepseek_api_fixed.php?action=info' => '获取API信息',
                'GET /deepseek_api_fixed.php?action=test&license_key=xxx' => '测试API连接',
                'POST /deepseek_api_fixed.php?action=chat&license_key=xxx' => '发送聊天消息',
                'POST /deepseek_api_fixed.php?action=validate_key&license_key=xxx' => '验证API密钥'
            ],
            'server_info' => [
                'php_version' => PHP_VERSION,
                'curl_enabled' => function_exists('curl_init'),
                'openssl_enabled' => extension_loaded('openssl'),
                'pdo_enabled' => extension_loaded('pdo'),
                'putenv_available' => function_exists('putenv')
            ]
        ];
        
        $this->respondSuccess($info, 'DeepSeek API信息');
    }
    
    private function testAPI() {
        try {
            // 测试数据库连接
            $stmt = $this->db->query("SELECT 1 as test");
            $dbTest = $stmt->fetch();
            
            $this->respondSuccess([
                'database_connection' => 'success',
                'test_query_result' => $dbTest,
                'api_status' => 'ready'
            ], 'API测试成功');
            
        } catch (Exception $e) {
            $this->respondError('API测试失败: ' . $e->getMessage(), 500);
        }
    }
    
    private function handleChat() {
        $apiKey = $this->request['api_key'] ?? '';
        $message = $this->request['message'] ?? '';
        
        if (empty($apiKey)) {
            $this->respondError('缺少api_key参数', 400);
        }
        
        if (empty($message)) {
            $this->respondError('缺少message参数', 400);
        }
        
        $result = $this->sendDeepSeekRequest($apiKey, $message);
        
        if ($result['success']) {
            $this->respondSuccess([
                'response' => $result['response'],
                'model' => 'deepseek-chat',
                'usage' => $result['usage'] ?? null
            ], 'DeepSeek聊天成功');
        } else {
            $this->respondError('DeepSeek聊天失败: ' . $result['error'], 500);
        }
    }
    
    private function validateApiKey() {
        $apiKey = $this->request['api_key'] ?? '';
        
        if (empty($apiKey)) {
            $this->respondError('缺少api_key参数', 400);
        }
        
        $validation = $this->validateDeepSeekApiKey($apiKey);
        
        if ($validation['valid']) {
            $this->respondSuccess([
                'is_valid' => true,
                'message' => 'API密钥验证成功'
            ], 'API密钥验证成功');
        } else {
            $this->respondError('API密钥验证失败: ' . $validation['error'], 400);
        }
    }
    
    private function getConfig() {
        $this->respondSuccess([
            'enabled' => true,
            'model' => 'deepseek-chat',
            'thinking_enabled' => false,
            'reply_delay' => 0,
            'system_prompt' => '你是一名专业的客服人员，请用友好的语言回答用户问题。'
        ], '配置获取成功');
    }
    
    private function getStatus() {
        $this->respondSuccess([
            'enabled' => true,
            'status' => 'ready',
            'message' => 'DeepSeek API已就绪'
        ], '状态获取成功');
    }
    
    private function sendDeepSeekRequest($apiKey, $message) {
        try {
            $url = 'https://api.deepseek.com/v1/chat/completions';
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ];
            
            $data = [
                'model' => 'deepseek-chat',
                'messages' => [
                    ['role' => 'user', 'content' => $message]
                ],
                'max_tokens' => 1000,
                'temperature' => 0.7
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return ['success' => false, 'error' => 'cURL错误: ' . $error];
            }
            
            $responseData = json_decode($response, true);
            
            if ($httpCode === 200 && isset($responseData['choices'])) {
                return [
                    'success' => true,
                    'response' => $responseData['choices'][0]['message']['content'],
                    'usage' => $responseData['usage'] ?? null
                ];
            }
            
            $errorMsg = $responseData['error']['message'] ?? '请求失败';
            return ['success' => false, 'error' => $errorMsg];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => '请求异常: ' . $e->getMessage()];
        }
    }
    
    private function validateDeepSeekApiKey($apiKey) {
        try {
            $url = 'https://api.deepseek.com/v1/chat/completions';
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ];
            
            $data = [
                'model' => 'deepseek-chat',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello']
                ],
                'max_tokens' => 10
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return ['valid' => false, 'error' => 'cURL错误: ' . $error];
            }
            
            if ($httpCode === 200) {
                $responseData = json_decode($response, true);
                if (isset($responseData['choices'])) {
                    return ['valid' => true, 'error' => null];
                }
            }
            
            $responseData = json_decode($response, true);
            $errorMsg = $responseData['error']['message'] ?? 'API密钥验证失败';
            
            return ['valid' => false, 'error' => $errorMsg];
            
        } catch (Exception $e) {
            return ['valid' => false, 'error' => '验证异常: ' . $e->getMessage()];
        }
    }
}

// 创建API实例并处理请求
new DeepSeekAPIFixed();
?>
